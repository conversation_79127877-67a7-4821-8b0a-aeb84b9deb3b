#!/usr/bin/env python3
"""
智能MCP调用系统 - 真正的通用性实现
自动发现服务能力，智能匹配用户需求，自动参数转换
"""
import logging
import json
from typing import Dict, Any, List, Tuple, Optional

logger = logging.getLogger(__name__)

class IntelligentMCPCaller:
    """智能MCP调用器 - 实现真正的通用性"""
    
    def __init__(self):
        self.service_capabilities = {}  # 缓存服务能力
        self.platform_mappings = {
            # 平台名称到ID的映射（基于hotnews服务的实际参数）
            'zhihu': 1, '知乎': 1,
            '36kr': 2, '36氪': 2,
            'baidu': 3, '百度': 3,
            'bilibili': 4, 'b站': 4, '哔哩哔哩': 4,
            'weibo': 5, '微博': 5,
            'douyin': 6, '抖音': 6,
            'hupu': 7, '虎扑': 7,
            'douban': 8, '豆瓣': 8,
            'it': 9, 'it新闻': 9
        }
    
    def discover_service_capabilities(self, server_name: str) -> Dict[str, Any]:
        """发现服务能力"""
        try:
            from .universal_mcp import universal_mcp
            
            # 检查缓存
            if server_name in self.service_capabilities:
                return self.service_capabilities[server_name]
            
            # 获取工具列表
            tools_result = universal_mcp.get_available_tools(server_name)
            
            if not tools_result.get('success'):
                logger.error(f"无法获取服务 {server_name} 的工具列表: {tools_result.get('error')}")
                return {}
            
            tools = tools_result.get('tools', [])
            capabilities = {
                'tools': tools,
                'tool_count': len(tools),
                'capabilities_summary': self._analyze_capabilities(tools)
            }
            
            # 缓存结果
            self.service_capabilities[server_name] = capabilities
            logger.info(f"发现服务 {server_name} 的能力: {len(tools)} 个工具")
            
            return capabilities
            
        except Exception as e:
            logger.error(f"发现服务能力失败: {server_name}, 错误: {e}")
            return {}
    
    def _analyze_capabilities(self, tools: List[Dict]) -> Dict[str, Any]:
        """分析服务能力"""
        summary = {
            'keywords': [],
            'platforms': [],
            'data_types': []
        }
        
        for tool in tools:
            name = tool.get('name', '').lower()
            desc = tool.get('description', '').lower()
            
            # 分析关键词
            if 'news' in name or 'news' in desc or '新闻' in desc:
                summary['keywords'].append('news')
            if 'hot' in name or 'hot' in desc or '热' in desc:
                summary['keywords'].append('hot')
            if 'search' in name or 'search' in desc or '搜索' in desc:
                summary['keywords'].append('search')
            
            # 分析支持的平台（基于参数描述）
            schema = tool.get('inputSchema', {})
            properties = schema.get('properties', {})
            for prop_name, prop_info in properties.items():
                prop_desc = prop_info.get('description', '')
                if 'zhihu' in prop_desc.lower() or '知乎' in prop_desc:
                    summary['platforms'].append('zhihu')
                if 'weibo' in prop_desc.lower() or '微博' in prop_desc:
                    summary['platforms'].append('weibo')
        
        return summary
    
    def intelligent_call(self, user_intent: str, preferred_server: str = None) -> Tuple[bool, str]:
        """智能调用 - 根据用户意图自动选择服务和工具"""
        try:
            from .universal_mcp import universal_mcp
            
            # 1. 确定目标服务
            if preferred_server:
                target_servers = [preferred_server]
            else:
                target_servers = self._select_suitable_servers(user_intent)
            
            # 2. 尝试每个服务
            for server_name in target_servers:
                logger.info(f"尝试服务: {server_name}")
                
                # 发现服务能力
                capabilities = self.discover_service_capabilities(server_name)
                if not capabilities:
                    continue
                
                # 选择最佳工具
                best_tool, best_args = self._select_best_tool(user_intent, capabilities)
                if not best_tool:
                    continue
                
                logger.info(f"选择工具: {best_tool}, 参数: {best_args}")
                
                # 执行调用
                success, result = universal_mcp.use_mcp_tool(server_name, best_tool, best_args)
                
                if success:
                    logger.info(f"✅ 智能调用成功: {server_name}.{best_tool}")
                    return True, result
                else:
                    logger.warning(f"调用失败: {result}")
            
            return False, "未找到合适的MCP服务来处理此请求"
            
        except Exception as e:
            logger.error(f"智能调用失败: {e}")
            return False, f"智能调用失败: {str(e)}"
    
    def _select_suitable_servers(self, user_intent: str) -> List[str]:
        """根据用户意图选择合适的服务"""
        intent_lower = user_intent.lower()
        suitable_servers = []
        
        # 基于关键词匹配
        if any(keyword in intent_lower for keyword in ['热榜', '热搜', '热点', '新闻', '微博', '知乎']):
            suitable_servers.append('hotnews')
        
        if any(keyword in intent_lower for keyword in ['搜索', 'search', '查找']):
            suitable_servers.append('duckduckgo-remote')
        
        if any(keyword in intent_lower for keyword in ['文件', 'file', '目录']):
            suitable_servers.append('files')
        
        # 如果没有匹配，返回所有可用服务
        if not suitable_servers:
            try:
                from .mcp_client import mcp_manager
                for server_name, config in mcp_manager.get_all_servers().items():
                    if config.get('enabled', False):
                        suitable_servers.append(server_name)
            except:
                pass
        
        return suitable_servers
    
    def _select_best_tool(self, user_intent: str, capabilities: Dict[str, Any]) -> Tuple[Optional[str], Dict[str, Any]]:
        """选择最佳工具和参数"""
        tools = capabilities.get('tools', [])
        if not tools:
            return None, {}
        
        # 简单策略：选择第一个工具（大多数服务只有一个主要工具）
        best_tool = tools[0]
        tool_name = best_tool.get('name')
        
        # 生成参数
        args = self._generate_arguments(user_intent, best_tool)
        
        return tool_name, args
    
    def _generate_arguments(self, user_intent: str, tool_info: Dict[str, Any]) -> Dict[str, Any]:
        """根据用户意图和工具信息生成参数"""
        args = {}
        intent_lower = user_intent.lower()
        
        schema = tool_info.get('inputSchema', {})
        properties = schema.get('properties', {})
        
        for param_name, param_info in properties.items():
            param_type = param_info.get('type')
            param_desc = param_info.get('description', '').lower()
            
            if param_name == 'sources' and param_type == 'array':
                # 处理hotnews的sources参数
                detected_platforms = []
                for platform_name, platform_id in self.platform_mappings.items():
                    if platform_name in intent_lower:
                        detected_platforms.append(platform_id)
                
                if detected_platforms:
                    args['sources'] = detected_platforms
                else:
                    # 默认使用知乎
                    args['sources'] = [1]
            
            elif param_name in ['query', 'q'] and param_type == 'string':
                # 处理搜索查询参数
                # 提取关键词
                keywords = []
                for word in user_intent.split():
                    if word not in ['使用', '查询', '搜索', '最新', '条', '个']:
                        keywords.append(word)
                args['query'] = ' '.join(keywords) if keywords else user_intent
            
            elif param_name in ['limit', 'count', 'num'] and param_type in ['integer', 'number']:
                # 处理数量参数
                import re
                numbers = re.findall(r'\d+', user_intent)
                if numbers:
                    args[param_name] = int(numbers[0])
                else:
                    args[param_name] = 10  # 默认10条
        
        return args

# 创建全局实例
intelligent_mcp = IntelligentMCPCaller()
