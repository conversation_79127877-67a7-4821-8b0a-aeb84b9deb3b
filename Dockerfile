# Use official Python image
FROM python:3.12-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_ROOT_USER_ACTION=ignore

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    gosu \
    dnsutils \
    iputils-ping \
    net-tools \
    ca-certificates \
    procps \
    psmisc \
    && rm -rf /var/lib/apt/lists/* \
    && update-ca-certificates

# Configure time zone
RUN unlink /etc/localtime && ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# Install Node.js 18.x
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get update \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/* \
    && npm cache clean --force \
    && npm --version \
    && node --version

# Install MCP SDK packages globally
# RUN npm install -g @modelcontextprotocol/sdk @smithery/sdk @modelcontextprotocol/server-filesystem

# Set working directory
WORKDIR /app

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser -m appuser

# Copy application code
COPY . .

# Set entrypoint script permissions
RUN chmod +x /app/docker-entrypoint.sh

# Expose Streamlit default port
EXPOSE 8501

# Set environment variables
ENV HOME=/home/<USER>

# Set entrypoint
ENTRYPOINT ["/app/docker-entrypoint.sh"]

# Run the Streamlit app
CMD ["streamlit", "run", "app.py", "--server.port=8501", "--server.address=0.0.0.0"]


